import { type SWRConfiguration } from 'swr';

// Default SWR configuration based on app-config.yml
export function createSWRConfig(appConfig: any): SWRConfiguration {
  const apiConfig = appConfig?.api || {
    baseUrl: '/api',
    timeout: 30000,
    retries: 3,
    retryDelay: 1000,
  };

  const featuresConfig = appConfig?.features || {
    enableRealTimeUpdates: true,
  };

  // Create fetcher with timeout and base URL handling
  const fetcher = async (url: string) => {
    // Handle relative URLs by prepending base URL
    const fullUrl = url.startsWith('http') ? url : `${apiConfig.baseUrl}${url}`;

    // Create AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), apiConfig.timeout);

    try {
      const response = await fetch(fullUrl, {
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const error = new Error('An error occurred while fetching the data.');
        (error as any).info = await response.json().catch(() => ({}));
        (error as any).status = response.status;
        throw error;
      }

      return response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  };

  return {
    fetcher,

    // Revalidation settings
    revalidateOnFocus: true,
    revalidateOnReconnect: true,
    revalidateIfStale: true,

    // Real-time updates based on feature flags
    refreshInterval: featuresConfig.enableRealTimeUpdates ? 30000 : 0, // 30 seconds if enabled

    // Cache settings based on performance config
    dedupingInterval: 2000, // 2 seconds
    focusThrottleInterval: 5000, // 5 seconds

    // Error retry configuration based on API config
    errorRetryCount: apiConfig.retries,
    errorRetryInterval: apiConfig.retryDelay,

    // Use exponential backoff for retries
    onErrorRetry: (
      error: any,
      _key: string,
      _config: any,
      revalidate: any,
      { retryCount }: any
    ) => {
      // Never retry on 404
      if (error.status === 404) return;

      // Never retry on 403 (forbidden)
      if (error.status === 403) return;

      // Only retry up to configured times
      if (retryCount >= apiConfig.retries) return;

      // Exponential backoff
      setTimeout(
        () => revalidate({ retryCount }),
        apiConfig.retryDelay * Math.pow(2, retryCount)
      );
    },

    // Global error handler
    onError: (error: any, key: string) => {
      console.error('SWR Error:', { error, key });

      // Don't show error notifications for 403/404
      if (error.status === 403 || error.status === 404) {
        return;
      }
    },

    // Cache provider - use Map for better performance
    provider: () => new Map(),

    // Suspense support (optional)
    suspense: false,

    // Keep previous data while revalidating
    keepPreviousData: true,

    // Loading timeout
    loadingTimeout: apiConfig.timeout,

    // Compare function for data equality
    compare: (a: any, b: any) => {
      // Custom comparison logic if needed
      return JSON.stringify(a) === JSON.stringify(b);
    },
  };
}

// Utility function to create optimistic update options
export function createOptimisticOptions(options?: {
  rollbackOnError?: boolean;
  revalidate?: boolean;
  populateCache?: boolean;
}) {
  return {
    rollbackOnError: options?.rollbackOnError ?? true,
    revalidate: options?.revalidate ?? true,
    populateCache: options?.populateCache ?? true,
  };
}

// Utility function to create cache keys
export function createCacheKey(endpoint: string, params?: Record<string, any>) {
  if (!params || Object.keys(params).length === 0) {
    return endpoint;
  }

  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.append(key, String(value));
    }
  });

  return `${endpoint}?${searchParams.toString()}`;
}

// Utility function for pagination cache keys
export function createPaginationKey(
  endpoint: string,
  page: number,
  limit: number,
  filters?: Record<string, any>
) {
  const params = { page, limit, ...filters };
  return createCacheKey(endpoint, params);
}
