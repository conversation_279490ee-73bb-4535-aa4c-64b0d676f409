// MSW configuration and environment settings

export interface MockConfig {
  enabled: boolean;
  environment: string;
  baseDelay: number;
  maxDelay: number;
  errorRate: number;
  enableLogging: boolean;
  enableErrorSimulation: boolean;
  enableNetworkDelay: boolean;
  scenarios: {
    [key: string]: {
      enabled: boolean;
      probability: number;
      description: string;
    };
  };
}

// Default configuration
const defaultConfig: MockConfig = {
  enabled: true,
  environment: 'development',
  baseDelay: 200,
  maxDelay: 1000,
  errorRate: 0.02, // 2% error rate
  enableLogging: true,
  enableErrorSimulation: true,
  enableNetworkDelay: true,
  scenarios: {
    slowNetwork: {
      enabled: false,
      probability: 0.1,
      description: 'Simulate slow network conditions',
    },
    serverError: {
      enabled: true,
      probability: 0.02,
      description: 'Simulate server errors',
    },
    timeout: {
      enabled: false,
      probability: 0.01,
      description: 'Simulate request timeouts',
    },
    unauthorized: {
      enabled: true,
      probability: 0.005,
      description: 'Simulate unauthorized responses',
    },
    maintenance: {
      enabled: false,
      probability: 0,
      description: 'Simulate maintenance mode',
    },
  },
};




// Get current configuration
export const getMockConfig = (): MockConfig => {
  return defaultConfig;
};

// Update configuration at runtime
let currentConfig = getMockConfig();

export const updateMockConfig = (updates: Partial<MockConfig>): MockConfig => {
  currentConfig = {
    ...currentConfig,
    ...updates,
    scenarios: {
      ...currentConfig.scenarios,
      ...updates.scenarios,
    },
  };
  return currentConfig;
};

export const getCurrentConfig = (): MockConfig => currentConfig;

// Scenario management
export const enableScenario = (
  scenarioName: string,
  probability?: number
): void => {
  if (currentConfig.scenarios[scenarioName]) {
    currentConfig.scenarios[scenarioName].enabled = true;
    if (probability !== undefined) {
      currentConfig.scenarios[scenarioName].probability = probability;
    }
  }
};

export const disableScenario = (scenarioName: string): void => {
  if (currentConfig.scenarios[scenarioName]) {
    currentConfig.scenarios[scenarioName].enabled = false;
  }
};

export const isScenarioEnabled = (scenarioName: string): boolean => {
  return currentConfig.scenarios[scenarioName]?.enabled || false;
};

export const getScenarioProbability = (scenarioName: string): number => {
  return currentConfig.scenarios[scenarioName]?.probability || 0;
};

// Logging utilities
export const logMockRequest = (
  method: string,
  url: string,
  status: number,
  duration: number
): void => {
  if (currentConfig.enableLogging) {
    console.log(`[MSW] ${method} ${url} - ${status} (${duration}ms)`);
  }
};

export const logMockError = (
  method: string,
  url: string,
  error: string
): void => {
  if (currentConfig.enableLogging) {
    console.warn(`[MSW] ${method} ${url} - ERROR: ${error}`);
  }
};

// Configuration validation
export const validateConfig = (config: Partial<MockConfig>): string[] => {
  const errors: string[] = [];

  if (config.baseDelay !== undefined && config.baseDelay < 0) {
    errors.push('baseDelay must be non-negative');
  }

  if (config.maxDelay !== undefined && config.maxDelay < 0) {
    errors.push('maxDelay must be non-negative');
  }

  if (
    config.baseDelay !== undefined &&
    config.maxDelay !== undefined &&
    config.baseDelay > config.maxDelay
  ) {
    errors.push('baseDelay cannot be greater than maxDelay');
  }

  if (
    config.errorRate !== undefined &&
    (config.errorRate < 0 || config.errorRate > 1)
  ) {
    errors.push('errorRate must be between 0 and 1');
  }

  if (config.scenarios) {
    Object.entries(config.scenarios).forEach(([name, scenario]) => {
      if (scenario.probability < 0 || scenario.probability > 1) {
        errors.push(`Scenario '${name}' probability must be between 0 and 1`);
      }
    });
  }

  return errors;
};

// Export configuration presets
export const configPresets = {
  // Special presets
  fastTesting: {
    enabled: true,
    baseDelay: 0,
    maxDelay: 50,
    errorRate: 0,
    enableLogging: false,
    enableErrorSimulation: false,
    enableNetworkDelay: false,
    scenarios: {},
  },

  errorTesting: {
    enabled: true,
    baseDelay: 100,
    maxDelay: 300,
    errorRate: 0.2,
    enableLogging: true,
    enableErrorSimulation: true,
    enableNetworkDelay: true,
    scenarios: {
      slowNetwork: {
        enabled: true,
        probability: 0.3,
        description: 'Simulate slow network conditions',
      },
      serverError: {
        enabled: true,
        probability: 0.2,
        description: 'Simulate server errors',
      },
      timeout: {
        enabled: true,
        probability: 0.1,
        description: 'Simulate request timeouts',
      },
      unauthorized: {
        enabled: true,
        probability: 0.1,
        description: 'Simulate unauthorized responses',
      },
      maintenance: {
        enabled: false,
        probability: 0,
        description: 'Simulate maintenance mode',
      },
    },
  },

  slowNetwork: {
    enabled: true,
    baseDelay: 1000,
    maxDelay: 5000,
    errorRate: 0.05,
    enableLogging: true,
    enableErrorSimulation: true,
    enableNetworkDelay: true,
    scenarios: {
      slowNetwork: {
        enabled: true,
        probability: 0.8,
        description: 'Simulate slow network conditions',
      },
      timeout: {
        enabled: true,
        probability: 0.2,
        description: 'Simulate request timeouts',
      },
    },
  },
};
