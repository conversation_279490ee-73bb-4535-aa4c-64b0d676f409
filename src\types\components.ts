// Component Types
// Centralized type definitions for UI components

import type { ReactNode, CSSProperties, HTMLAttributes } from 'react';
import type { Size, Variant, Color, Position, Direction } from './common';

// Base Component Props
export interface BaseComponentProps {
  className?: string;
  style?: CSSProperties;
  children?: ReactNode;
  'data-testid'?: string;
  id?: string;
}

// Interactive Component Props
export interface InteractiveComponentProps extends BaseComponentProps {
  disabled?: boolean;
  loading?: boolean;
  onClick?: (event: React.MouseEvent) => void;
  onFocus?: (event: React.FocusEvent) => void;
  onBlur?: (event: React.FocusEvent) => void;
  onKeyDown?: (event: React.KeyboardEvent) => void;
  tabIndex?: number;
  'aria-label'?: string;
  'aria-describedby'?: string;
}

// Button Component Types
export interface ButtonProps extends InteractiveComponentProps {
  variant?: Variant;
  size?: Size;
  color?: Color;
  fullWidth?: boolean;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
  type?: 'button' | 'submit' | 'reset';
  href?: string;
  target?: '_blank' | '_self' | '_parent' | '_top';
  download?: boolean | string;
}

// Input Component Types
export interface InputProps extends BaseComponentProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
  value?: string | number;
  defaultValue?: string | number;
  placeholder?: string;
  disabled?: boolean;
  readonly?: boolean;
  required?: boolean;
  autoFocus?: boolean;
  autoComplete?: string;
  maxLength?: number;
  minLength?: number;
  min?: number;
  max?: number;
  step?: number;
  pattern?: string;
  size?: Size;
  variant?: 'default' | 'filled' | 'outlined';
  error?: boolean;
  helperText?: string;
  label?: string;
  prefix?: ReactNode;
  suffix?: ReactNode;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
}

// Select Component Types
export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
  icon?: ReactNode;
  description?: string;
}

export interface SelectProps extends BaseComponentProps {
  value?: string | number | (string | number)[];
  defaultValue?: string | number | (string | number)[];
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  multiple?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  size?: Size;
  variant?: 'default' | 'filled' | 'outlined';
  error?: boolean;
  helperText?: string;
  label?: string;
  options: SelectOption[];
  loading?: boolean;
  loadingText?: string;
  noOptionsText?: string;
  maxHeight?: number;
  onChange?: (value: string | number | (string | number)[]) => void;
  onFocus?: (event: React.FocusEvent) => void;
  onBlur?: (event: React.FocusEvent) => void;
  onSearch?: (query: string) => void;
}

// Modal Component Types
export interface ModalProps extends BaseComponentProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  size?: Size | 'fullscreen';
  centered?: boolean;
  closable?: boolean;
  maskClosable?: boolean;
  keyboard?: boolean;
  destroyOnClose?: boolean;
  footer?: ReactNode;
  header?: ReactNode;
  zIndex?: number;
  mask?: boolean;
  maskStyle?: CSSProperties;
  bodyStyle?: CSSProperties;
  width?: string | number;
  height?: string | number;
  onAfterOpen?: () => void;
  onAfterClose?: () => void;
}

// Dropdown Component Types
export interface DropdownProps extends BaseComponentProps {
  trigger?: 'click' | 'hover' | 'contextMenu';
  placement?: Position;
  offset?: [number, number];
  arrow?: boolean;
  disabled?: boolean;
  open?: boolean;
  defaultOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  overlay: ReactNode;
  overlayClassName?: string;
  overlayStyle?: CSSProperties;
  getPopupContainer?: () => HTMLElement;
}

// Tooltip Component Types
export interface TooltipProps extends BaseComponentProps {
  title: ReactNode;
  placement?: Position;
  trigger?: 'hover' | 'focus' | 'click';
  open?: boolean;
  defaultOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  arrow?: boolean;
  offset?: [number, number];
  delay?: number;
  mouseEnterDelay?: number;
  mouseLeaveDelay?: number;
  overlayClassName?: string;
  overlayStyle?: CSSProperties;
  getPopupContainer?: () => HTMLElement;
}

// Table Component Types
export interface TableColumn<T = any> {
  key: string;
  title: ReactNode;
  dataIndex?: keyof T;
  width?: string | number;
  minWidth?: string | number;
  maxWidth?: string | number;
  align?: 'left' | 'center' | 'right';
  fixed?: 'left' | 'right';
  sortable?: boolean;
  filterable?: boolean;
  resizable?: boolean;
  ellipsis?: boolean;
  render?: (value: any, record: T, index: number) => ReactNode;
  sorter?: boolean | ((a: T, b: T) => number);
  filters?: { text: string; value: any }[];
  onFilter?: (value: any, record: T) => boolean;
  filterDropdown?: ReactNode;
  filterIcon?: ReactNode;
  filterMultiple?: boolean;
  defaultSortOrder?: 'ascend' | 'descend';
  defaultFilteredValue?: any[];
}

export interface TableProps<T = any> extends BaseComponentProps {
  columns: TableColumn<T>[];
  dataSource: T[];
  rowKey?: string | ((record: T) => string);
  loading?: boolean;
  size?: Size;
  bordered?: boolean;
  showHeader?: boolean;
  sticky?: boolean;
  scroll?: { x?: string | number; y?: string | number };
  pagination?: false | PaginationProps;
  rowSelection?: RowSelectionProps<T>;
  expandable?: ExpandableProps<T>;
  summary?: (data: T[]) => ReactNode;
  sortDirections?: ('ascend' | 'descend')[];
  showSorterTooltip?: boolean;
  onChange?: (pagination: any, filters: any, sorter: any, extra: any) => void;
  onRow?: (record: T, index?: number) => HTMLAttributes<HTMLTableRowElement>;
  onHeaderRow?: (columns: TableColumn<T>[], index?: number) => HTMLAttributes<HTMLTableRowElement>;
}

// Pagination Component Types
export interface PaginationProps extends BaseComponentProps {
  current?: number;
  defaultCurrent?: number;
  total: number;
  pageSize?: number;
  defaultPageSize?: number;
  pageSizeOptions?: string[];
  showSizeChanger?: boolean;
  showQuickJumper?: boolean;
  showTotal?: (total: number, range: [number, number]) => ReactNode;
  size?: Size;
  simple?: boolean;
  disabled?: boolean;
  hideOnSinglePage?: boolean;
  itemRender?: (page: number, type: 'page' | 'prev' | 'next' | 'jump-prev' | 'jump-next', originalElement: ReactNode) => ReactNode;
  onChange?: (page: number, pageSize: number) => void;
  onShowSizeChange?: (current: number, size: number) => void;
}

// Row Selection Types
export interface RowSelectionProps<T = any> {
  type?: 'checkbox' | 'radio';
  selectedRowKeys?: (string | number)[];
  defaultSelectedRowKeys?: (string | number)[];
  onChange?: (selectedRowKeys: (string | number)[], selectedRows: T[]) => void;
  onSelect?: (record: T, selected: boolean, selectedRows: T[], nativeEvent: Event) => void;
  onSelectAll?: (selected: boolean, selectedRows: T[], changeRows: T[]) => void;
  onSelectInvert?: (selectedRowKeys: (string | number)[]) => void;
  getCheckboxProps?: (record: T) => { disabled?: boolean; name?: string };
  selections?: TableRowSelection[] | boolean;
  hideSelectAll?: boolean;
  fixed?: boolean;
  columnWidth?: string | number;
  columnTitle?: ReactNode;
  checkStrictly?: boolean;
}

// Table Row Selection
export interface TableRowSelection {
  key: string;
  text: ReactNode;
  onSelect?: (changeableRowKeys: (string | number)[]) => void;
}

// Expandable Types
export interface ExpandableProps<T = any> {
  expandedRowKeys?: (string | number)[];
  defaultExpandedRowKeys?: (string | number)[];
  expandedRowRender?: (record: T, index: number, indent: number, expanded: boolean) => ReactNode;
  expandRowByClick?: boolean;
  expandIcon?: (props: ExpandIconProps<T>) => ReactNode;
  onExpand?: (expanded: boolean, record: T) => void;
  onExpandedRowsChange?: (expandedKeys: (string | number)[]) => void;
  defaultExpandAllRows?: boolean;
  indentSize?: number;
  expandIconColumnIndex?: number;
  showExpandColumn?: boolean;
  fixed?: boolean | 'left' | 'right';
  columnWidth?: string | number;
  columnTitle?: ReactNode;
}

// Expand Icon Props
export interface ExpandIconProps<T = any> {
  prefixCls: string;
  expanded: boolean;
  record: T;
  expandable: boolean;
  onExpand: (record: T, event: React.MouseEvent) => void;
}

// Form Component Types
export interface FormProps extends BaseComponentProps {
  layout?: 'horizontal' | 'vertical' | 'inline';
  size?: Size;
  disabled?: boolean;
  colon?: boolean;
  hideRequiredMark?: boolean;
  labelAlign?: 'left' | 'right';
  labelWrap?: boolean;
  labelCol?: { span?: number; offset?: number };
  wrapperCol?: { span?: number; offset?: number };
  validateTrigger?: string | string[];
  preserve?: boolean;
  scrollToFirstError?: boolean | ScrollOptions;
  requiredMark?: boolean | 'optional';
  onFinish?: (values: any) => void;
  onFinishFailed?: (errorInfo: any) => void;
  onFieldsChange?: (changedFields: any[], allFields: any[]) => void;
  onValuesChange?: (changedValues: any, allValues: any) => void;
}

// Form Item Props
export interface FormItemProps extends BaseComponentProps {
  name?: string | (string | number)[];
  label?: ReactNode;
  labelAlign?: 'left' | 'right';
  labelCol?: { span?: number; offset?: number };
  wrapperCol?: { span?: number; offset?: number };
  help?: ReactNode;
  extra?: ReactNode;
  validateStatus?: 'success' | 'warning' | 'error' | 'validating';
  hasFeedback?: boolean;
  required?: boolean;
  hidden?: boolean;
  noStyle?: boolean;
  tooltip?: ReactNode;
  colon?: boolean;
  htmlFor?: string;
  preserve?: boolean;
  validateFirst?: boolean;
  validateTrigger?: string | string[];
  rules?: FormRule[];
  dependencies?: (string | number)[][];
  shouldUpdate?: boolean | ((prevValues: any, currentValues: any) => boolean);
  trigger?: string;
  valuePropName?: string;
  getValueFromEvent?: (...args: any[]) => any;
  getValueProps?: (value: any) => any;
  normalize?: (value: any, prevValue: any, allValues: any) => any;
  initialValue?: any;
  messageVariables?: Record<string, string>;
}

// Form Rule
export interface FormRule {
  enum?: any[];
  len?: number;
  max?: number;
  message?: string;
  min?: number;
  pattern?: RegExp;
  required?: boolean;
  transform?: (value: any) => any;
  type?: 'string' | 'number' | 'boolean' | 'method' | 'regexp' | 'integer' | 'float' | 'array' | 'object' | 'enum' | 'date' | 'url' | 'hex' | 'email';
  validator?: (rule: any, value: any, callback: any) => Promise<void> | void;
  whitespace?: boolean;
  warningOnly?: boolean;
}

// Card Component Types
export interface CardProps extends BaseComponentProps {
  title?: ReactNode;
  extra?: ReactNode;
  bordered?: boolean;
  hoverable?: boolean;
  loading?: boolean;
  size?: Size;
  type?: 'inner';
  cover?: ReactNode;
  actions?: ReactNode[];
  bodyStyle?: CSSProperties;
  headStyle?: CSSProperties;
  tabList?: CardTabListType[];
  tabBarExtraContent?: ReactNode;
  activeTabKey?: string;
  defaultActiveTabKey?: string;
  onTabChange?: (key: string) => void;
  tabProps?: any;
}

// Card Tab List Type
export interface CardTabListType {
  key: string;
  tab: ReactNode;
  disabled?: boolean;
}

// Layout Component Types
export interface LayoutProps extends BaseComponentProps {
  direction?: Direction;
  hasSider?: boolean;
}

export interface SiderProps extends BaseComponentProps {
  breakpoint?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl';
  collapsed?: boolean;
  collapsedWidth?: number | string;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  reverseArrow?: boolean;
  theme?: 'light' | 'dark';
  trigger?: ReactNode;
  width?: number | string;
  zeroWidthTriggerStyle?: CSSProperties;
  onBreakpoint?: (broken: boolean) => void;
  onCollapse?: (collapsed: boolean, type: 'clickTrigger' | 'responsive') => void;
}

export interface HeaderProps extends BaseComponentProps {
  // Additional header-specific props can be added here
}

export interface ContentProps extends BaseComponentProps {
  // Additional content-specific props can be added here
}

export interface FooterProps extends BaseComponentProps {
  // Additional footer-specific props can be added here
}
