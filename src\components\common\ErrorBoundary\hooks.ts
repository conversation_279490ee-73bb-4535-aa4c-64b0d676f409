import { useContext, useCallback } from 'react';
import { ErrorBoundaryContext } from './ErrorBoundaryProvider';
import type { ErrorBoundaryContextValue } from './ErrorBoundaryProvider';

/**
 * Hook to use error boundary context
 */
export const useErrorBoundaryContext = (): ErrorBoundaryContextValue => {
  const context = useContext(ErrorBoundaryContext);
  
  if (!context) {
    throw new Error('useErrorBoundaryContext must be used within an ErrorBoundaryProvider');
  }
  
  return context;
};

/**
 * Hook for global error reporting
 */
export const useGlobalErrorHandler = () => {
  const { reportError, clearErrors, errorHistory, globalErrorCount } = useErrorBoundaryContext();

  const reportGlobalError = useCallback((
    error: Error | string | unknown,
    context?: Record<string, any>
  ) => {
    reportError(error, {
      ...context,
      reportedViaHook: true,
      timestamp: new Date().toISOString(),
    });
  }, [reportError]);

  return {
    reportError: reportGlobalError,
    clearErrors,
    errorHistory,
    globalErrorCount,
    hasErrors: errorHistory.length > 0,
  };
};
