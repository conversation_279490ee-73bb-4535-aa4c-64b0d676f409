// Configuration Types
// Centralized type definitions for application configuration

import type { Environment, Theme, Language, TimeFormat, DateFormat } from './app';

// Main Application Configuration
export interface AppConfiguration {
  // Application Info
  app: AppInfo;
  
  // API Configuration
  api: ApiConfig;
  
  // Feature Flags
  features: FeatureFlags;
  
  // UI Configuration
  ui: UIConfig;
  
  // Performance Settings
  performance: PerformanceConfig;
  
  // Security Settings
  security: SecurityConfig;
  
  // Logging Configuration
  logging: LoggingConfig;
  
  // Integration Settings
  integrations: IntegrationConfig;
  
  // Backup Settings
  backup: BackupConfig;
  
  // Monitoring Settings
  monitoring: MonitoringConfig;
  
  // Development Settings (only in dev/staging)
  development?: DevelopmentConfig;
}

// Application Information
export interface AppInfo {
  name: string;
  version: string;
  description: string;
  environment: Environment;
  buildNumber?: string;
  buildDate?: string;
  gitCommit?: string;
  gitBranch?: string;
}

// API Configuration
export interface ApiConfig {
  baseUrl: string;
  timeout: number;
  retries: number;
  retryDelay: number;
  enableCaching: boolean;
  cacheTimeout: number;
  enableMocking: boolean;
  mockDelay: number;
  endpoints: Record<string, string>;
  headers: Record<string, string>;
}

// Feature Flags
export interface FeatureFlags {
  // Core Features
  enableAnalytics: boolean;
  enableNotifications: boolean;
  enableDarkMode: boolean;
  enableRealTimeUpdates: boolean;
  
  // Advanced Features
  enableAdvancedSearch: boolean;
  enableBetaFeatures: boolean;
  enableExperimentalUI: boolean;
  enableDebugMode: boolean;
  
  // Enterprise Features
  enableSSOLogin: boolean;
  enableAuditLogging: boolean;
  enableDataExport: boolean;
  enableAdvancedReporting: boolean;
  
  // Module Features
  enableSalesModule: boolean;
  enableUserManagement: boolean;
  enableTaskManagement: boolean;
  enableFileManagement: boolean;
  enableCalendarModule: boolean;
  enableReportsModule: boolean;
  enableSettingsModule: boolean;
}

// UI Configuration
export interface UIConfig {
  // Theme Configuration
  defaultTheme: Theme;
  allowThemeToggle: boolean;
  customThemes: CustomTheme[];
  
  // Layout Settings
  sidebarCollapsed: boolean;
  compactMode: boolean;
  showTooltips: boolean;
  animationsEnabled: boolean;
  reducedMotion: boolean;
  highContrastMode: boolean;
  
  // Data Display
  itemsPerPage: number;
  maxItemsPerPage: number;
  enableVirtualScrolling: boolean;
  enableInfiniteScroll: boolean;
  
  // Accessibility
  enableScreenReader: boolean;
  enableKeyboardNavigation: boolean;
  focusIndicatorVisible: boolean;
  
  // Localization
  defaultLanguage: Language;
  supportedLanguages: Language[];
  dateFormat: DateFormat;
  timeFormat: TimeFormat;
  timezone: string;
  enableRTL: boolean;
}

// Custom Theme
export interface CustomTheme {
  id: string;
  name: string;
  colors: Record<string, string>;
  fonts: Record<string, string>;
  spacing: Record<string, string>;
  borderRadius: Record<string, string>;
  shadows: Record<string, string>;
}

// Performance Configuration
export interface PerformanceConfig {
  // Caching
  enableCaching: boolean;
  cacheTimeout: number;
  maxCacheSize: number;
  
  // Loading
  enableLazyLoading: boolean;
  enableCodeSplitting: boolean;
  preloadCriticalResources: boolean;
  
  // Optimization
  enableImageOptimization: boolean;
  enableAssetCompression: boolean;
  enableServiceWorker: boolean;
  
  // Memory Management
  maxMemoryUsage: number;
  enableMemoryMonitoring: boolean;
  garbageCollectionInterval: number;
}

// Security Configuration
export interface SecurityConfig {
  // Authentication
  sessionTimeout: number;
  maxLoginAttempts: number;
  lockoutDuration: number;
  passwordMinLength: number;
  passwordRequireSpecialChars: boolean;
  enableTwoFactor: boolean;
  
  // Authorization
  enableRBAC: boolean;
  defaultPermissions: string[];
  adminPermissions: string[];
  
  // Data Protection
  enableEncryption: boolean;
  encryptionAlgorithm: string;
  enableDataMasking: boolean;
  enableAuditTrail: boolean;
  
  // Network Security
  enableCSP: boolean;
  enableCORS: boolean;
  allowedOrigins: string[];
  enableHTTPS: boolean;
  enableHSTS: boolean;
}

// Logging Configuration
export interface LoggingConfig {
  level: 'error' | 'warn' | 'info' | 'debug' | 'trace';
  enableConsoleLogging: boolean;
  enableFileLogging: boolean;
  enableRemoteLogging: boolean;
  maxLogSize: number;
  logRetentionDays: number;
  logFormat: 'json' | 'text';
  enableStructuredLogging: boolean;
  sensitiveFields: string[];
}

// Integration Configuration
export interface IntegrationConfig {
  // External Services
  enableGoogleMaps: boolean;
  enableStripePayments: boolean;
  enableSlackNotifications: boolean;
  enableZendeskSupport: boolean;
  
  // Social Login
  enableGoogleLogin: boolean;
  enableMicrosoftLogin: boolean;
  enableGitHubLogin: boolean;
  enableLinkedInLogin: boolean;
  
  // Analytics
  enableGoogleAnalytics: boolean;
  enableMixpanel: boolean;
  enableHotjar: boolean;
  
  // Error Tracking
  enableSentry: boolean;
  enableBugsnag: boolean;
  enableRollbar: boolean;
  
  // Communication
  enableTwilio: boolean;
  enableSendGrid: boolean;
  enableMailchimp: boolean;
}

// Backup Configuration
export interface BackupConfig {
  enableAutoBackup: boolean;
  backupInterval: number;
  retentionPeriod: number;
  enableCloudBackup: boolean;
  backupLocation: string;
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
}

// Monitoring Configuration
export interface MonitoringConfig {
  enableHealthChecks: boolean;
  healthCheckInterval: number;
  enablePerformanceMonitoring: boolean;
  enableErrorTracking: boolean;
  enableUptimeMonitoring: boolean;
  enableResourceMonitoring: boolean;
  alertThresholds: AlertThresholds;
}

// Alert Thresholds
export interface AlertThresholds {
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  responseTime: number;
  errorRate: number;
  uptimePercentage: number;
}

// Development Configuration
export interface DevelopmentConfig {
  enableHotReload: boolean;
  enableSourceMaps: boolean;
  enableProfiling: boolean;
  enableStorybook: boolean;
  enableTestMode: boolean;
  mockApiResponses: boolean;
  enableDevLogin: boolean;
  enableDebugPanel: boolean;
  enablePerformancePanel: boolean;
  enableReduxDevTools: boolean;
}

// Configuration validation schema
export interface ConfigValidationSchema {
  required: string[];
  optional: string[];
  types: Record<string, string>;
  constraints: Record<string, any>;
}

// Configuration loader options
export interface ConfigLoaderOptions {
  environment: Environment;
  configPath?: string;
  enableValidation: boolean;
  enableCaching: boolean;
  enableWatching: boolean;
  fallbackConfig?: Partial<AppConfiguration>;
}
