/**
 * ErrorBoundaryProvider Component
 * Global error boundary provider with context for application-wide error handling
 */

import React, { createContext, useContext, useCallback, useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import ErrorBoundary from './ErrorBoundary';
import type { ErrorBoundaryProps } from './ErrorBoundary';
import type { AppError } from '../../../utils/errorTypes';
import { createAppError } from '../../../utils/errorTypes';
import { reportError } from '../../../utils/errorReporting';

// Error boundary context interface
export interface ErrorBoundaryContextValue {
  reportError: (error: Error | string | unknown, context?: Record<string, any>) => void;
  clearErrors: () => void;
  errorHistory: AppError[];
  globalErrorCount: number;
  isGlobalErrorHandlingEnabled: boolean;
  setGlobalErrorHandling: (enabled: boolean) => void;
}

// Create context
const ErrorBoundaryContext = createContext<ErrorBoundaryContextValue | null>(null);

// Provider props
export interface ErrorBoundaryProviderProps {
  children: ReactNode;
  maxGlobalErrors?: number;
  enableGlobalErrorHandling?: boolean;
  enableUnhandledRejectionCapture?: boolean;
  enableGlobalErrorCapture?: boolean;
  onGlobalError?: (error: AppError) => void;
  fallbackComponent?: React.ComponentType<any>;
  defaultErrorBoundaryProps?: Partial<ErrorBoundaryProps>;
}

/**
 * Global Error Boundary Provider
 */
export const ErrorBoundaryProvider: React.FC<ErrorBoundaryProviderProps> = ({
  children,
  maxGlobalErrors = 10,
  enableGlobalErrorHandling = true,
  enableUnhandledRejectionCapture = true,
  enableGlobalErrorCapture = true,
  onGlobalError,
  fallbackComponent,
  defaultErrorBoundaryProps = {},
}) => {
  const [errorHistory, setErrorHistory] = useState<AppError[]>([]);
  const [globalErrorCount, setGlobalErrorCount] = useState(0);
  const [isGlobalErrorHandlingEnabled, setIsGlobalErrorHandlingEnabled] = useState(enableGlobalErrorHandling);

  // Global error handler
  const handleGlobalError = useCallback((error: Error | string | unknown, context?: Record<string, any>) => {
    const appError = createAppError(error, undefined, {
      globalError: true,
      timestamp: new Date(),
      ...context,
    });

    // Update error history
    setErrorHistory(prev => {
      const newHistory = [...prev, appError].slice(-maxGlobalErrors);
      return newHistory;
    });

    setGlobalErrorCount(prev => prev + 1);

    // Call custom global error handler
    if (onGlobalError) {
      try {
        onGlobalError(appError);
      } catch (handlerError) {
        console.error('Error in global error handler:', handlerError);
      }
    }

    // Report error
    reportError(appError, {
      globalErrorHandler: true,
      errorCount: globalErrorCount + 1,
      ...context,
    }).catch(reportingError => {
      console.error('Failed to report global error:', reportingError);
    });

    return appError;
  }, [maxGlobalErrors, onGlobalError, globalErrorCount]);

  // Clear all errors
  const clearErrors = useCallback(() => {
    setErrorHistory([]);
    setGlobalErrorCount(0);
  }, []);

  // Report error function for context
  const reportErrorToContext = useCallback((
    error: Error | string | unknown, 
    context?: Record<string, any>
  ) => {
    if (isGlobalErrorHandlingEnabled) {
      handleGlobalError(error, context);
    }
  }, [handleGlobalError, isGlobalErrorHandlingEnabled]);

  // Set up global error listeners
  useEffect(() => {
    if (!isGlobalErrorHandlingEnabled) return;

    const handleWindowError = (event: ErrorEvent) => {
      if (enableGlobalErrorCapture) {
        handleGlobalError(event.error || event.message, {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          type: 'window-error',
        });
      }
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      if (enableUnhandledRejectionCapture) {
        handleGlobalError(event.reason, {
          type: 'unhandled-rejection',
          promise: event.promise,
        });
      }
    };

    // Add event listeners
    window.addEventListener('error', handleWindowError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    // Cleanup
    return () => {
      window.removeEventListener('error', handleWindowError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [
    isGlobalErrorHandlingEnabled,
    enableGlobalErrorCapture,
    enableUnhandledRejectionCapture,
    handleGlobalError,
  ]);

  // Context value
  const contextValue: ErrorBoundaryContextValue = {
    reportError: reportErrorToContext,
    clearErrors,
    errorHistory,
    globalErrorCount,
    isGlobalErrorHandlingEnabled,
    setGlobalErrorHandling: setIsGlobalErrorHandlingEnabled,
  };

  // Enhanced error boundary props
  const enhancedErrorBoundaryProps: Omit<ErrorBoundaryProps, 'children'> = {
    level: 'page',
    enableAutoRecovery: true,
    enableReporting: true,
    enablePerformanceMonitoring: true,
    enableAccessibilityFeatures: true,
    componentName: 'GlobalErrorBoundary',
    onError: (error, errorInfo) => {
      handleGlobalError(error, {
        componentStack: errorInfo.componentStack,
        globalBoundary: true,
      });
    },
    fallback: fallbackComponent,
    ...defaultErrorBoundaryProps,
  };

  return (
    <ErrorBoundaryContext.Provider value={contextValue}>
      <ErrorBoundary {...enhancedErrorBoundaryProps}>
        {children}
      </ErrorBoundary>
    </ErrorBoundaryContext.Provider>
  );
};

// Hooks and HOC are now exported from separate files to avoid react-refresh warnings
// Import from './hooks' and './withErrorBoundary' instead

export default ErrorBoundaryProvider;