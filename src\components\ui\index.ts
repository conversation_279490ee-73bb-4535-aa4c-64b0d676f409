// UI Components - Specialized UI components for specific use cases

// Phone Input (specialized input component)
export { default as PhoneInput } from './PhoneInput/PhoneInput';
export type { PhoneInputProps } from './PhoneInput/PhoneInput';

// Dashboard Components
export { default as NotificationBar } from './NotificationBar/NotificationBar';
export type { NotificationBarProps } from './NotificationBar/NotificationBar';

export { default as AppTile } from './AppTile/AppTile';
export type { AppTileProps } from './AppTile/AppTile';

export { default as SearchOverlay } from './SearchOverlay/SearchOverlay';
export type { SearchOverlayProps } from './SearchOverlay/SearchOverlay';

export { default as CustomerSupportModal } from './CustomerSupportModal/CustomerSupportModal';
export type { CustomerSupportModalProps } from './CustomerSupportModal/CustomerSupportModal';


export { default as CenteredSearchChipInput } from './CenteredSearchChipInput/CenteredSearchChipInput';
export type {
  CenteredSearchChipInputProps,
  ChipData,
  FilterOption,
} from './CenteredSearchChipInput/CenteredSearchChipInput';

// Typography Components (remaining specialized ones)
export { default as Caption } from './Caption/Caption';
export type { CaptionProps } from './Caption/Caption';

// Company Selector
export { default as CompanySelector } from './CompanySelector/CompanySelector';
export type { CompanySelectorProps } from './CompanySelector/CompanySelector';
// Modal Components (remaining specialized ones)
export { RelativeModal } from './RelativeModal';
export type { RelativeModalProps } from './RelativeModal';


