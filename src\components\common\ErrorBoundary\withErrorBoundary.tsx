import React from 'react';
import ErrorBoundary from './ErrorBoundary';
import type { ErrorBoundaryProps } from './ErrorBoundary';

/**
 * Higher-order component for wrapping components with error boundaries
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Partial<ErrorBoundaryProps>
) {
  const WrappedComponent = React.forwardRef<any, P>((props, ref) => (
    <ErrorBoundary
      componentName={Component.displayName || Component.name}
      level="component"
      enableAutoRecovery={true}
      enableReporting={true}
      {...errorBoundaryProps}
    >
      <Component {...props} ref={ref} />
    </ErrorBoundary>
  ));

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}
