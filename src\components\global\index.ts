// Navigation Components
export { default as AppNotFound } from './AppNotFound';
export type { AppNotFoundProps } from './AppNotFound';

export { default as Breadcrumb } from './Breadcrumb';
export type { BreadcrumbProps, BreadcrumbItem } from './Breadcrumb';

// Typography Components
export { Text } from './Text';
export type { TextProps } from './Text';

export { Heading } from './Heading';
export type { HeadingProps } from './Heading';

// Layout Components
export { Card } from './Card';
export type { CardProps } from './Card';

export { Separator } from './Separator';
export type { SeparatorProps } from './Separator';

// Unified View Mode Selector - replaces ViewModeSwitcher and ViewToggle
export { ViewModeSelector } from './ViewModeSelector';
export type {
  ViewModeSelectorProps,
  ViewModeOption,
  ViewModeVariant,
  ViewModeSize
} from './ViewModeSelector';
export {
  dataViewModes,
  simpleViewModes,
  discussViewModes,
  tableViewModes
} from './ViewModeSelector/presets';

// Enhanced Dropdown System
export {
  Dropdown,
  FilterDropdown,
  DropdownBase,
  DropdownTrigger,
  DropdownContent,
  DropdownItem,
  DropdownSection,
  DropdownSeparator,
  useDropdownContext,
} from './Dropdown';
export type {
  DropdownProps,
  DropdownWithSectionsProps,
  BaseDropdownProps,
  DropdownItemType,
  DropdownSectionType,
  DropdownAlign,
  DropdownSize,
  DropdownVariant,
} from './Dropdown';

// Theme Components
export { default as ThemeToggle } from './ThemeToggle/ThemeToggle';

// User Interface Components
export { default as UserAvatarDropdown } from './UserAvatarDropdown/UserAvatarDropdown';
