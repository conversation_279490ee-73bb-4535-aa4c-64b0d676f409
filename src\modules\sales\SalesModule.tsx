import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { useThemeStore } from '../../stores/themeStore';
import DynamicAppHeader from '../../components/layout/DynamicAppHeader';
import AppDynamicContent from '../../components/layout/AppDynamicContent';
import { getAppById } from '../../mocks/data/apps';
import { DashboardView } from './components/views/DashboardView';
import { OrdersView } from './components/views/OrdersView';
import { CustomersView } from './components/views/CustomersView';
import { ProductsView } from './components/views/ProductsView';
import { ReportsView } from './components/views/ReportsView';

export interface SalesModuleProps {
  className?: string;
  'data-testid'?: string;
}

/**
 * Sales Module - Complete sales management and CRM functionality
 * 
 * Features:
 * - Customer management and CRM
 * - Sales order processing
 * - Product catalog management
 * - Sales analytics and reporting
 * - Quote and proposal generation
 */
const SalesModule: React.FC<SalesModuleProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const [searchParams] = useSearchParams();
  const { colors } = useThemeStore();
  
  const view = searchParams.get('view') || 'dashboard';
  const app = getAppById('1'); // Sales app ID

  // Mock user data - in real app this would come from auth context
  const user = {
    name: 'Sales Manager',
    email: '<EMAIL>',
    avatar: '👤',
  };

  const renderView = () => {
    switch (view) {
      case 'dashboard':
        return <DashboardView />;
      case 'orders':
        return <OrdersView />;
      case 'customers':
        return <CustomersView />;
      case 'products':
        return <ProductsView />;
      case 'reports':
        return <ReportsView />;
      default:
        return <DashboardView />;
    }
  };

  const getViewConfig = () => {
    switch (view) {
      case 'dashboard':
        return {
          title: 'Sales Dashboard',
          description: 'Overview of sales performance and key metrics',
          showSearch: false,
          showFilters: false,
          showViewModes: false,
        };
      case 'orders':
        return {
          title: 'Sales Orders',
          description: 'Manage and track sales orders',
          showSearch: true,
          showFilters: true,
          showViewModes: true,
        };
      case 'customers':
        return {
          title: 'Customers',
          description: 'Customer relationship management',
          showSearch: true,
          showFilters: true,
          showViewModes: true,
        };
      case 'products':
        return {
          title: 'Products',
          description: 'Product catalog and inventory',
          showSearch: true,
          showFilters: true,
          showViewModes: true,
        };
      case 'reports':
        return {
          title: 'Sales Reports',
          description: 'Analytics and performance reports',
          showSearch: false,
          showFilters: true,
          showViewModes: false,
        };
      default:
        return {
          title: 'Sales Dashboard',
          description: 'Overview of sales performance and key metrics',
          showSearch: false,
          showFilters: false,
          showViewModes: false,
        };
    }
  };

  const viewConfig = getViewConfig();

  return (
    <div 
      className={`flex flex-col h-screen ${className}`}
      style={{ backgroundColor: colors.background }}
      data-testid={testId}
    >
      {/* App Header */}
      <DynamicAppHeader
        app={app}
        user={user}
        view={{
          title: viewConfig.title,
          description: viewConfig.description,
        }}
        data-testid="sales-header"
      />

      {/* Main Content with Bottom Bar */}
      <AppDynamicContent
        view={{
          title: viewConfig.title,
          description: viewConfig.description,
          showSearch: viewConfig.showSearch,
          showFilters: viewConfig.showFilters,
          showViewModes: viewConfig.showViewModes,
        }}
        data-testid="sales-content"
      >
        {/* Sales Module Content */}
        <div className="flex-1 overflow-hidden">
          {renderView()}
        </div>
      </AppDynamicContent>
    </div>
  );
};

export default SalesModule;
