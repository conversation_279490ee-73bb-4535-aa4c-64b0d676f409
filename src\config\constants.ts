// Application Constants
// Centralized constants used throughout the application

// API Constants
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    REGISTER: '/auth/register',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    VERIFY_EMAIL: '/auth/verify-email',
    CHANGE_PASSWORD: '/auth/change-password',
  },
  
  // Users
  USERS: {
    BASE: '/users',
    PROFILE: '/users/profile',
    PREFERENCES: '/users/preferences',
    AVATAR: '/users/avatar',
    NOTIFICATIONS: '/users/notifications',
    SESSIONS: '/users/sessions',
  },
  
  // Applications
  APPS: {
    BASE: '/apps',
    REGISTRY: '/apps/registry',
    CONFIG: '/apps/config',
    PERMISSIONS: '/apps/permissions',
  },
  
  // Files
  FILES: {
    BASE: '/files',
    UPLOAD: '/files/upload',
    DOWNLOAD: '/files/download',
    PREVIEW: '/files/preview',
    METADATA: '/files/metadata',
  },
  
  // Notifications
  NOTIFICATIONS: {
    BASE: '/notifications',
    MARK_READ: '/notifications/mark-read',
    MARK_ALL_READ: '/notifications/mark-all-read',
    PREFERENCES: '/notifications/preferences',
  },
  
  // Settings
  SETTINGS: {
    BASE: '/settings',
    SYSTEM: '/settings/system',
    USER: '/settings/user',
    APP: '/settings/app',
  },
  
  // Health Check
  HEALTH: '/health',
  
  // WebSocket
  WEBSOCKET: '/ws',
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  // Success
  OK: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NO_CONTENT: 204,
  
  // Redirection
  MOVED_PERMANENTLY: 301,
  FOUND: 302,
  NOT_MODIFIED: 304,
  
  // Client Error
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  
  // Server Error
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  // Authentication
  ACCESS_TOKEN: 'nexed_access_token',
  REFRESH_TOKEN: 'nexed_refresh_token',
  USER_SESSION: 'nexed_user_session',
  
  // User Preferences
  USER_PREFERENCES: 'nexed_user_preferences',
  THEME: 'nexed_theme',
  LANGUAGE: 'nexed_language',
  SIDEBAR_COLLAPSED: 'nexed_sidebar_collapsed',
  
  // Application State
  CURRENT_APP: 'nexed_current_app',
  APP_SETTINGS: 'nexed_app_settings',
  VIEW_MODE: 'nexed_view_mode',
  
  // Cache
  API_CACHE: 'nexed_api_cache',
  FORM_DRAFTS: 'nexed_form_drafts',
  
  // Development
  DEV_SETTINGS: 'nexed_dev_settings',
  DEBUG_MODE: 'nexed_debug_mode',
} as const;

// Event Names
export const EVENTS = {
  // Authentication
  USER_LOGIN: 'user:login',
  USER_LOGOUT: 'user:logout',
  SESSION_EXPIRED: 'session:expired',
  TOKEN_REFRESHED: 'token:refreshed',
  
  // Navigation
  ROUTE_CHANGED: 'route:changed',
  APP_CHANGED: 'app:changed',
  
  // UI
  THEME_CHANGED: 'theme:changed',
  SIDEBAR_TOGGLED: 'sidebar:toggled',
  MODAL_OPENED: 'modal:opened',
  MODAL_CLOSED: 'modal:closed',
  
  // Data
  DATA_LOADED: 'data:loaded',
  DATA_UPDATED: 'data:updated',
  DATA_DELETED: 'data:deleted',
  
  // Notifications
  NOTIFICATION_RECEIVED: 'notification:received',
  NOTIFICATION_READ: 'notification:read',
  
  // Errors
  ERROR_OCCURRED: 'error:occurred',
  ERROR_RESOLVED: 'error:resolved',
  
  // WebSocket
  WS_CONNECTED: 'ws:connected',
  WS_DISCONNECTED: 'ws:disconnected',
  WS_MESSAGE: 'ws:message',
  WS_ERROR: 'ws:error',
} as const;

// CSS Classes
export const CSS_CLASSES = {
  // Layout
  CONTAINER: 'nexed-container',
  SIDEBAR: 'nexed-sidebar',
  HEADER: 'nexed-header',
  CONTENT: 'nexed-content',
  FOOTER: 'nexed-footer',
  
  // Components
  BUTTON: 'nexed-button',
  INPUT: 'nexed-input',
  MODAL: 'nexed-modal',
  DROPDOWN: 'nexed-dropdown',
  TABLE: 'nexed-table',
  CARD: 'nexed-card',
  
  // States
  LOADING: 'nexed-loading',
  ERROR: 'nexed-error',
  SUCCESS: 'nexed-success',
  WARNING: 'nexed-warning',
  DISABLED: 'nexed-disabled',
  ACTIVE: 'nexed-active',
  SELECTED: 'nexed-selected',
  
  // Themes
  LIGHT: 'nexed-theme-light',
  DARK: 'nexed-theme-dark',
  
  // Responsive
  MOBILE: 'nexed-mobile',
  TABLET: 'nexed-tablet',
  DESKTOP: 'nexed-desktop',
} as const;

// Breakpoints
export const BREAKPOINTS = {
  XS: 480,
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  XXL: 1536,
} as const;

// Z-Index Layers
export const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
  TOAST: 1080,
  LOADING: 1090,
} as const;

// Animation Durations (in milliseconds)
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  EXTRA_SLOW: 1000,
} as const;

// File Size Limits
export const FILE_SIZE_LIMITS = {
  AVATAR: 2 * 1024 * 1024, // 2MB
  IMAGE: 10 * 1024 * 1024, // 10MB
  DOCUMENT: 50 * 1024 * 1024, // 50MB
  VIDEO: 100 * 1024 * 1024, // 100MB
  GENERAL: 25 * 1024 * 1024, // 25MB
} as const;

// Supported File Types
export const FILE_TYPES = {
  IMAGES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
  DOCUMENTS: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/csv',
  ],
  VIDEOS: ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov'],
  AUDIO: ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/aac', 'audio/flac'],
  ARCHIVES: ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed'],
} as const;

// Date Formats
export const DATE_FORMATS = {
  ISO: 'YYYY-MM-DD',
  US: 'MM/DD/YYYY',
  EU: 'DD/MM/YYYY',
  DISPLAY: 'MMM DD, YYYY',
  FULL: 'dddd, MMMM DD, YYYY',
  TIME: 'HH:mm:ss',
  TIME_12H: 'h:mm A',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  DATETIME_DISPLAY: 'MMM DD, YYYY h:mm A',
} as const;

// Validation Patterns
export const VALIDATION_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^\+?[\d\s\-()]+$/,
  URL: /^https?:\/\/.+/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  USERNAME: /^[a-zA-Z0-9_-]+$/,
  SLUG: /^[a-z0-9-]+$/,
  HEX_COLOR: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
  IPV4: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  REQUIRED: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email address',
  INVALID_PHONE: 'Please enter a valid phone number',
  INVALID_URL: 'Please enter a valid URL',
  PASSWORD_TOO_SHORT: 'Password must be at least 8 characters long',
  PASSWORD_WEAK: 'Password must contain uppercase, lowercase, number, and special character',
  PASSWORDS_DONT_MATCH: 'Passwords do not match',
  FILE_TOO_LARGE: 'File size exceeds the maximum limit',
  INVALID_FILE_TYPE: 'File type is not supported',
  NETWORK_ERROR: 'Network error. Please check your connection.',
  SERVER_ERROR: 'Server error. Please try again later.',
  UNAUTHORIZED: 'You are not authorized to perform this action',
  FORBIDDEN: 'Access denied',
  NOT_FOUND: 'The requested resource was not found',
  VALIDATION_ERROR: 'Please check your input and try again',
  UNKNOWN_ERROR: 'An unexpected error occurred',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  SAVED: 'Changes saved successfully',
  CREATED: 'Created successfully',
  UPDATED: 'Updated successfully',
  DELETED: 'Deleted successfully',
  UPLOADED: 'File uploaded successfully',
  SENT: 'Sent successfully',
  COPIED: 'Copied to clipboard',
  LOGGED_IN: 'Logged in successfully',
  LOGGED_OUT: 'Logged out successfully',
  PASSWORD_CHANGED: 'Password changed successfully',
  EMAIL_VERIFIED: 'Email verified successfully',
  INVITATION_SENT: 'Invitation sent successfully',
} as const;

// Keyboard Shortcuts
export const KEYBOARD_SHORTCUTS = {
  // Global
  SEARCH: 'Ctrl+K',
  HELP: '?',
  SETTINGS: 'Ctrl+,',
  
  // Navigation
  SIDEBAR_TOGGLE: 'Ctrl+B',
  NEXT_TAB: 'Ctrl+Tab',
  PREV_TAB: 'Ctrl+Shift+Tab',
  
  // Actions
  SAVE: 'Ctrl+S',
  COPY: 'Ctrl+C',
  PASTE: 'Ctrl+V',
  UNDO: 'Ctrl+Z',
  REDO: 'Ctrl+Y',
  
  // Modal
  CLOSE_MODAL: 'Escape',
  CONFIRM: 'Enter',
  
  // Table
  SELECT_ALL: 'Ctrl+A',
  DELETE: 'Delete',
} as const;

// Default Values
export const DEFAULTS = {
  PAGE_SIZE: 25,
  DEBOUNCE_DELAY: 300,
  TOAST_DURATION: 5000,
  TOOLTIP_DELAY: 500,
  ANIMATION_DURATION: 300,
  API_TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
  CACHE_TIMEOUT: 300000, // 5 minutes
  SESSION_TIMEOUT: 3600000, // 1 hour
} as const;
