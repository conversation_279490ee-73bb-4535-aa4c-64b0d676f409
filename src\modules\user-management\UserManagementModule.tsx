import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { useThemeStore } from '../../stores/themeStore';
import DynamicAppHeader from '../../components/layout/DynamicAppHeader';
import AppDynamicContent from '../../components/layout/AppDynamicContent';
import { getAppById } from '../../mocks/data/apps';
import { UserList } from './components/UserList';
import { AddUserModal } from './components/AddUserModal';

export interface UserManagementModuleProps {
  className?: string;
  'data-testid'?: string;
}

/**
 * User Management Module - Complete user administration functionality
 * 
 * Features:
 * - User CRUD operations
 * - Role and permission management
 * - User profile management
 * - Optimistic updates
 * - Advanced filtering and search
 */
const UserManagementModule: React.FC<UserManagementModuleProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const [searchParams] = useSearchParams();
  const { colors } = useThemeStore();
  
  const view = searchParams.get('view') || 'users';
  const app = getAppById('2'); // User Management app ID

  // Mock user data - in real app this would come from auth context
  const user = {
    name: 'Admin User',
    email: '<EMAIL>',
    avatar: '👤',
  };

  const getViewConfig = () => {
    switch (view) {
      case 'users':
        return {
          title: 'User Management',
          description: 'Manage users, roles, and permissions',
          showSearch: true,
          showFilters: true,
          showViewModes: true,
        };
      case 'roles':
        return {
          title: 'Role Management',
          description: 'Configure user roles and permissions',
          showSearch: true,
          showFilters: false,
          showViewModes: false,
        };
      case 'permissions':
        return {
          title: 'Permission Management',
          description: 'Manage system permissions and access control',
          showSearch: true,
          showFilters: true,
          showViewModes: false,
        };
      default:
        return {
          title: 'User Management',
          description: 'Manage users, roles, and permissions',
          showSearch: true,
          showFilters: true,
          showViewModes: true,
        };
    }
  };

  const renderView = () => {
    switch (view) {
      case 'users':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold" style={{ color: colors.text }}>
                Users
              </h2>
              <AddUserModal />
            </div>
            <UserList />
          </div>
        );
      case 'roles':
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold" style={{ color: colors.text }}>
              Role Management
            </h2>
            <div className="text-center py-12" style={{ color: colors.textSecondary }}>
              Role management interface coming soon...
            </div>
          </div>
        );
      case 'permissions':
        return (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold" style={{ color: colors.text }}>
              Permission Management
            </h2>
            <div className="text-center py-12" style={{ color: colors.textSecondary }}>
              Permission management interface coming soon...
            </div>
          </div>
        );
      default:
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold" style={{ color: colors.text }}>
                Users
              </h2>
              <AddUserModal />
            </div>
            <UserList />
          </div>
        );
    }
  };

  const viewConfig = getViewConfig();

  return (
    <div 
      className={`flex flex-col h-screen ${className}`}
      style={{ backgroundColor: colors.background }}
      data-testid={testId}
    >
      {/* App Header */}
      <DynamicAppHeader
        app={app}
        user={user}
        view={{
          title: viewConfig.title,
          description: viewConfig.description,
        }}
        data-testid="user-management-header"
      />

      {/* Main Content with Bottom Bar */}
      <AppDynamicContent
        view={{
          title: viewConfig.title,
          description: viewConfig.description,
          showSearch: viewConfig.showSearch,
          showFilters: viewConfig.showFilters,
          showViewModes: viewConfig.showViewModes,
        }}
        data-testid="user-management-content"
      >
        {/* User Management Module Content */}
        <div className="flex-1 overflow-hidden p-6">
          {renderView()}
        </div>
      </AppDynamicContent>
    </div>
  );
};

export default UserManagementModule;
